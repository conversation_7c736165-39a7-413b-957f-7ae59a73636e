load("@protobuf//bazel:proto_library.bzl", "proto_library")
load("@protobuf//bazel:java_proto_library.bzl", "java_proto_library")
load("@rules_go//go:def.bzl", "go_library")
load("@rules_go//proto:def.bzl", "go_proto_library", "go_grpc_library")
load("@grpc-java//:java_grpc_library.bzl", "java_grpc_library")

proto_library(
    name = "proto",
    srcs = glob(["*.proto"]),
    strip_import_prefix = "/proto",
    deps = [
        "//proto/common:proto",
        "@protobuf//:timestamp_proto",
        "@protobuf//:duration_proto",
        "@googleapis//google/type:date_proto",
    ],
    visibility = ["//visibility:public"],
)

java_proto_library(
    name = "proto_java",
    deps = [":proto"],
    visibility = ["//visibility:public"],
)

java_grpc_library(
    name = "grpc_java",
    srcs = [":proto"],
    deps = [":proto_java"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "proto_go",
    protos = [":proto"],
    deps = [
        "//proto/common:proto_go",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@googleapis//google/type:date_go_proto",
    ],
    importpath = "github.com/example/bazel-example/proto/user/testdatapb",
    visibility = ["//visibility:public"],
)

go_grpc_library(
    name = "grpc_go",
    protos = [":proto"],
    deps = [
        ":proto_go",
        "//proto/common:proto_go",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@googleapis//google/type:date_go_proto",
    ],
    importpath = "github.com/example/bazel-example/proto/user/testdatagrpc",
    visibility = ["//visibility:public"],
)
