syntax = "proto3";

package user.testdata;

import "user/testdata/marketplace.proto";
import "user/testdata/cryptocurrency.proto";
import "user/testdata/financial_services.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service RetailPosService {
  rpc ProcessSale(ProcessSaleRequest) returns (ProcessSaleResponse);
  rpc GetTransactionHistory(GetTransactionHistoryRequest) returns (stream Transaction);
  rpc GetProductInfo(GetProductInfoRequest) returns (Product);
}

message SaleItem {
  string product_id = 1;
  int32 quantity = 2;
  double price = 3;
}

message ProcessSaleRequest {
  repeated SaleItem items = 1;
}

message ProcessSaleResponse {
  Transaction transaction = 1;
  string error_message = 2;
}

message GetProductInfoRequest {
  string product_id = 1;
}